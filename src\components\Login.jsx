/**
 * @fileoverview Componente de autenticación principal del sistema SQQ
 * @description Componente que maneja el proceso de login con múltiples medidas de seguridad,
 * animaciones GSAP, detección de idioma automática, y sistema de logos rotativos.
 * Incluye validación robusta, rate limiting, y monitoreo de seguridad en tiempo real.
 * <AUTHOR>
 * @version 1.0.0
 */

import React, { useEffect, useRef, useState } from 'react';
import gsap from 'gsap';
import { useNavigate } from 'react-router-dom';
import { Eye, EyeOff, Moon, Sun } from 'lucide-react';
import secureStorage from '../utils/SecureStorage';
import securityMonitor from '../utils/SecurityMonitor';
import securityLogger from '../utils/SecurityLogger';
import { useAuth } from '../hooks/useAuth';
import { useDarkMode } from '../hooks';

// Importación de logos para sistema rotativo
import logo1 from '../assets/sequre-logo-negro.svg';
import logo2 from '../assets/sequre-logo-negro-17.svg';
import logo3 from '../assets/sequre-logo-negro-18.svg';
import logo4 from '../assets/sequre-logo-negro-19.svg';
import logo5 from '../assets/sequre-logo-negro-20.svg';

/**
 * Array de logos para rotación automática
 * @type {Array<string>} Lista de rutas de logos importados
 * @description Contiene los diferentes logos que se rotan automáticamente
 * para crear un efecto visual dinámico durante el proceso de login
 */
const logoList = [logo1, logo2, logo3, logo4, logo5];

/**
 * Estilos CSS para animaciones personalizadas del componente
 * @type {string} Definiciones CSS en formato string
 * @description Define animaciones keyframe para efectos visuales:
 * - fadeIn: Aparición gradual de elementos
 * - scaleIn: Escalado con aparición
 * - drawLine: Efecto de dibujo de líneas
 * - eyeBlink: Animación de parpadeo para iconos de ojo
 */
const eyeAnimationStyles = `
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes scaleIn {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
  }

  @keyframes drawLine {
    from { stroke-dasharray: 0 100; }
    to { stroke-dasharray: 100 0; }
  }

  @keyframes eyeBlink {
    0%, 90%, 100% { transform: scaleY(1); }
    95% { transform: scaleY(0.1); }
  }
`;

/**
 * Componente principal de autenticación
 * @component
 * @description Proporciona una interfaz completa de login con:
 * - Autenticación segura con validación robusta
 * - Sistema de logos rotativos animados
 * - Detección automática de idioma del navegador
 * - Rate limiting y monitoreo de seguridad
 * - Modo oscuro/claro con persistencia
 * - Animaciones GSAP profesionales
 * - Redirección automática según rol de usuario
 *
 * @returns {JSX.Element} Interfaz de login completamente funcional
 *
 * @example
 * // Uso básico del componente
 * <Login />
 *
 * @requires useAuth - Hook para autenticación
 * @requires useDarkMode - Hook para modo oscuro
 * @requires securityMonitor - Sistema de monitoreo de seguridad
 * @requires securityLogger - Sistema de logging de eventos
 */
const Login = () => {
  /**
   * Estados locales del componente de login
   * @description Gestiona todos los estados necesarios para el proceso de autenticación
   * y la experiencia de usuario durante el login
   */

  /** @type {string} Email/usuario ingresado por el usuario */
  const [usuario, setUsuario] = useState('');

  /** @type {string} Contraseña ingresada por el usuario */
  const [password, setPassword] = useState('');

  /** @type {boolean} Controla la visibilidad de la contraseña */
  const [showPassword, setShowPassword] = useState(false);

  /**
   * Estado del idioma de la interfaz
   * @type {string} Código de idioma ('es' | 'en')
   * @description Detecta automáticamente el idioma del navegador y permite
   * persistencia en localStorage para futuras sesiones
   */
  const [language, setLanguage] = useState(() => {
    // Detectar idioma del navegador automáticamente
    const browserLang = navigator.language.startsWith('es') ? 'es' : 'en';
    return localStorage.getItem('language') || browserLang;
  });

  /** @type {number} Índice del logo actual en la rotación automática */
  const [currentLogoIndex, setCurrentLogoIndex] = useState(0);

  /** @type {number} Contador de intentos de login fallidos */
  const [loginAttempts, setLoginAttempts] = useState(0);

  /** @type {boolean} Indica si el usuario está bloqueado por exceso de intentos */
  const [isBlocked, setIsBlocked] = useState(false);

  /** @type {Object} Objeto con errores de validación por campo */
  const [errors, setErrors] = useState({});

  /** @type {boolean} Controla la visibilidad de notificación de éxito */
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);

  /** @type {boolean} Controla la visibilidad de notificación de error */
  const [showErrorNotification, setShowErrorNotification] = useState(false);

  /** @type {string} Mensaje de error actual para mostrar al usuario */
  const [errorMessage, setErrorMessage] = useState('');

  /** @type {boolean} Indica si se está ejecutando la redirección post-login */
  const [isRedirecting, setIsRedirecting] = useState(false);

  /** @type {string} Tipo de usuario detectado durante el login */
  const [userType, setUserType] = useState('');

  /**
   * Hooks y referencias del componente
   * @description Inicialización de hooks personalizados y referencias para animaciones
   */

  /** Hook de navegación de React Router */
  const navigate = useNavigate();

  /** Hook de autenticación personalizado */
  const { login: authLogin, isLoading: authLoading } = useAuth();

  /** Hook de modo oscuro personalizado */
  const { darkMode, toggleDarkMode } = useDarkMode();

  /**
   * Sistema de traducciones integrado para el componente de login
   * @type {Object} Objeto con traducciones en español e inglés
   * @description Proporciona todas las cadenas de texto necesarias para la interfaz
   * de login en ambos idiomas soportados. Incluye mensajes de estado, errores,
   * y textos de la interfaz con soporte para interpolación de parámetros.
   *
   * @property {Object} es - Traducciones en español
   * @property {Object} en - Traducciones en inglés
   */
  const translations = {
    es: {
      welcome: '¡Bienvenido a Quantum!',
      subtitle: 'Por favor inicia sesión para continuar.',
      email: 'Email',
      password: 'Contraseña',
      login: 'Iniciar Sesión',
      loginSuccess: '¡Login Exitoso!',
      loginError: 'Error de Login',
      redirectingAdmin: 'Redirigiendo al panel de administración...',
      redirectingUser: 'Redirigiendo al panel de usuario...',
      blocked: '🚫 Bloqueado',
      loggingIn: 'Iniciando sesión...',
      blockedMessage: '🚫 Cuenta temporalmente bloqueada por seguridad. Espera 5 minutos.',
      attemptsWarning: '⚠️ Intentos fallidos: {attempts}/5. Ten cuidado con las credenciales.'
    },
    en: {
      welcome: 'Welcome to Quantum!',
      subtitle: 'Please log in to continue.',
      email: 'Email',
      password: 'Password',
      login: 'Log In',
      loginSuccess: 'Login Successful!',
      loginError: 'Login Error',
      redirectingAdmin: 'Redirecting to admin panel...',
      redirectingUser: 'Redirecting to user panel...',
      blocked: '🚫 Blocked',
      loggingIn: 'Logging in...',
      blockedMessage: '🚫 Account temporarily blocked for security. Wait 5 minutes.',
      attemptsWarning: '⚠️ Failed attempts: {attempts}/5. Be careful with credentials.'
    }
  };

  /**
   * Función de traducción con soporte para interpolación
   * @function t
   * @param {string} key - Clave de la traducción a obtener
   * @param {Object} [params={}] - Parámetros para interpolación en el texto
   * @returns {string} Texto traducido con parámetros interpolados
   * @description Obtiene una traducción según el idioma actual y permite
   * la interpolación de parámetros dinámicos usando la sintaxis {param}
   *
   * @example
   * // Traducción simple
   * t('welcome') // "¡Bienvenido a Quantum!" o "Welcome to Quantum!"
   *
   * // Traducción con parámetros
   * t('attemptsWarning', { attempts: 3 }) // "⚠️ Intentos fallidos: 3/5..."
   */
  const t = (key, params = {}) => {
    let text = translations[language][key] || key;
    Object.keys(params).forEach(param => {
      text = text.replace(`{${param}}`, params[param]);
    });
    return text;
  };

  /**
   * Referencias para animaciones GSAP específicas
   * @description Referencias DOM para controlar animaciones de elementos específicos
   */

  /** @type {React.RefObject} Referencia al panel izquierdo del layout */
  const leftPanelRef = useRef(null);

  /** @type {React.RefObject} Referencia al logo del panel derecho */
  const rightLogoRef = useRef(null);

  /** @type {React.RefObject} Referencia al logo en vista móvil */
  const mobileLogoRef = useRef(null);

  /** @type {React.RefObject} Referencia a la imagen del logo actual */
  const logoImgRef = useRef(null);

  ///-------------------------------------------------------------------------------------------------------------------------------------------------------------------///

  /**
   * FUNCIONES DE SEGURIDAD Y VALIDACIÓN
   * @description Conjunto de funciones especializadas para validación, sanitización
   * y manejo seguro de datos de entrada del usuario
   */

  /**
   * Función para mostrar notificaciones de error elegantes
   * @function showErrorNotificationWithMessage
   * @param {string} message - Mensaje de error a mostrar al usuario
   * @description Muestra una notificación de error con auto-ocultado después de 5 segundos.
   * Utiliza el sistema de estado local para controlar la visibilidad de la notificación.
   *
   * @example
   * showErrorNotificationWithMessage('Credenciales inválidas');
   */
  const showErrorNotificationWithMessage = (message) => {
    console.log('🚨 Mostrando notificación de error elegante:', message);
    setErrorMessage(message);
    setShowErrorNotification(true);

    // Auto-ocultar después de 5 segundos para mejor UX
    setTimeout(() => {
      setShowErrorNotification(false);
    }, 5000);
  };

  /**
   * Validación robusta de direcciones de email
   * @function validateEmail
   * @param {string} email - Dirección de email a validar
   * @returns {boolean} true si el email es válido, false en caso contrario
   * @description Valida formato de email usando regex estándar y verifica
   * longitud dentro de límites seguros (5-254 caracteres)
   *
   * @example
   * validateEmail('<EMAIL>') // true
   * validateEmail('invalid-email') // false
   */
  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 254 && email.length >= 5;
  };

  /**
   * Validación de contraseñas con criterios de seguridad
   * @function validatePassword
   * @param {string} password - Contraseña a validar
   * @returns {boolean} true si la contraseña cumple criterios, false en caso contrario
   * @description Valida longitud de contraseña entre 6 y 128 caracteres
   * para balance entre seguridad y usabilidad
   *
   * @example
   * validatePassword('123456') // true
   * validatePassword('12345') // false (muy corta)
   */
  const validatePassword = (password) => {
    return password.length >= 6 && password.length <= 128;
  };

  /**
   * Sanitización de inputs para prevenir ataques XSS
   * @function sanitizeInput
   * @param {string} input - Texto de entrada a sanitizar
   * @returns {string} Texto sanitizado sin caracteres peligrosos
   * @description Elimina espacios en blanco y caracteres potencialmente
   * peligrosos que podrían usarse en ataques de inyección
   *
   * @example
   * sanitizeInput('  <script>alert("xss")</script>  ') // 'scriptalert(xss)/script'
   */
  const sanitizeInput = (input) => {
    return input.trim().replace(/[<>'"]/g, '');
  };

  /**
   * Función de logging de eventos de seguridad
   * @function logSecurityEvent
   * @param {string} event - Tipo de evento de seguridad
   * @param {Object} details - Detalles adicionales del evento
   * @description Registra eventos de seguridad usando el logger centralizado
   * para auditoría y monitoreo del sistema
   *
   * @example
   * logSecurityEvent('LOGIN_ATTEMPT', { email: '<EMAIL>', success: false });
   */
  const logSecurityEvent = (event, details) => {
    securityLogger.logInfo(event, details);
  };

  /**
   * Gestor seguro de tokens de autenticación
   * @namespace TokenManager
   * @description Proporciona funciones para el manejo seguro de tokens de autenticación
   * con encriptación local, almacenamiento redundante y logging de seguridad.
   * Implementa doble almacenamiento (encriptado y sessionStorage) para compatibilidad.
   */
  const TokenManager = {
    /**
     * Almacena un token de autenticación de forma segura
     * @function setToken
     * @param {string} token - Token JWT de autenticación
     * @param {string} role - Rol del usuario ('admin' | 'usuario')
     * @description Almacena el token con encriptación XOR y también en sessionStorage
     * como backup. Establece expiración de 24 horas y registra el evento de seguridad.
     *
     * @example
     * TokenManager.setToken('eyJhbGciOiJIUzI1NiIs...', 'admin');
     */
    setToken: (token, role) => {
      const expiry = Date.now() + (24 * 60 * 60 * 1000); // 24 horas de validez
      const loginTime = Date.now();

      // Almacenar datos con encriptación XOR para seguridad adicional
      secureStorage.setSecure('auth_token', token);
      secureStorage.setSecure('user_role', role);
      secureStorage.setSecure('token_expiry', expiry);
      secureStorage.setSecure('login_time', loginTime);

      // Almacenamiento redundante en sessionStorage para compatibilidad
      sessionStorage.setItem('token', token);
      sessionStorage.setItem('userRole', role);
      sessionStorage.setItem('tokenExpiry', expiry.toString());
      sessionStorage.setItem('loginTime', loginTime.toString());

      // Registrar evento de seguridad para auditoría
      logSecurityEvent('TOKEN_CREATED', {
        role: role,
        expiry: new Date(expiry).toISOString(),
        encrypted: true
      });
    },

    /**
     * Limpia todos los tokens y datos de autenticación
     * @function clearToken
     * @description Elimina de forma segura todos los tokens almacenados
     * tanto en almacenamiento encriptado como en sessionStorage y localStorage.
     * Registra el evento de limpieza para auditoría.
     *
     * @example
     * TokenManager.clearToken(); // Limpia toda la sesión
     */
    clearToken: () => {
      // Limpiar datos del almacenamiento encriptado
      secureStorage.removeSecure('auth_token');
      secureStorage.removeSecure('user_role');
      secureStorage.removeSecure('token_expiry');
      secureStorage.removeSecure('login_time');

      // Limpiar sessionStorage actual
      sessionStorage.removeItem('token');
      sessionStorage.removeItem('userRole');
      sessionStorage.removeItem('tokenExpiry');
      sessionStorage.removeItem('loginTime');

      // Limpiar localStorage legacy para compatibilidad con versiones anteriores
      localStorage.removeItem('token');
      localStorage.removeItem('userRole');

      // Registrar evento de limpieza para auditoría
      logSecurityEvent('TOKEN_CLEARED', {
        timestamp: new Date().toISOString(),
        method: 'secure_clear'
      });
    },

    /**
     * Verifica si el token actual es válido y no ha expirado
     * @function isTokenValid
     * @returns {boolean} true si el token es válido, false si ha expirado o no existe
     * @description Verifica la validez del token consultando primero el almacenamiento
     * encriptado y luego el sessionStorage como fallback. Si el token ha expirado,
     * limpia automáticamente todos los datos de autenticación.
     *
     * @example
     * if (TokenManager.isTokenValid()) {
     *   // Usuario autenticado, continuar
     * } else {
     *   // Redirigir a login
     * }
     */
    isTokenValid: () => {
      // Verificar primero datos del almacenamiento encriptado (preferido)
      const encryptedToken = secureStorage.getSecure('auth_token');
      const encryptedExpiry = secureStorage.getSecure('token_expiry');

      if (encryptedToken && encryptedExpiry) {
        if (Date.now() > encryptedExpiry) {
          TokenManager.clearToken();
          logSecurityEvent('TOKEN_EXPIRED', {
            expiry: new Date(encryptedExpiry).toISOString()
          });
          return false;
        }
        return true;
      }

      // Fallback a sessionStorage para compatibilidad
      const token = sessionStorage.getItem('token');
      const expiry = sessionStorage.getItem('tokenExpiry');

      if (!token || !expiry) return false;

      if (Date.now() > parseInt(expiry)) {
        TokenManager.clearToken();
        return false;
      }

      return true;
    },

    /**
     * Obtiene el rol del usuario autenticado
     * @function getRole
     * @returns {string|null} Rol del usuario ('admin' | 'usuario') o null si no hay sesión
     * @description Obtiene el rol del usuario desde el almacenamiento encriptado
     * o sessionStorage como fallback
     *
     * @example
     * const userRole = TokenManager.getRole();
     * if (userRole === 'admin') {
     *   // Mostrar opciones de administrador
     * }
     */
    getRole: () => {
      return secureStorage.getSecure('user_role') || sessionStorage.getItem('userRole');
    }
  };

  ///-------------------------------------------------------------------------------------------------------------------------------------------------------------------///

  /**
   * SISTEMA DE ANIMACIONES GSAP
   * @description Efectos de animación para logos rotativos y entrada de elementos
   */

  /**
   * Efecto para animación rotativa de logos
   * @description Implementa un sistema de rotación automática de logos con animaciones
   * GSAP suaves. Espera 3 segundos antes de iniciar el ciclo y cambia logos cada segundo.
   * Incluye animaciones de fade out/in para transiciones suaves.
   *
   * @dependencies logoImgRef - Referencia al elemento imagen del logo
   * @cleanup Limpia timeouts e intervalos al desmontar el componente
   */
  useEffect(() => {
    let index = 0;

    /**
     * Función para ciclar entre logos con animación
     * @function cycleLogo
     * @description Anima la transición entre logos: fade out → cambio de imagen → fade in
     * Utiliza GSAP para animaciones suaves y profesionales
     */
    const cycleLogo = () => {
      index = (index + 1) % logoList.length;

      // Animación de transición: desaparece → cambia → reaparece
      gsap.to(logoImgRef.current, {
        opacity: 0,
        duration: 0.2,
        onComplete: () => {
          setCurrentLogoIndex(index);
          gsap.to(logoImgRef.current, { opacity: 1, duration: 0.2 });
        },
      });
    };

    // Configuración de timing: esperar 3 segundos antes de iniciar ciclo
    let intervalId;

    const timeoutId = setTimeout(() => {
      // Después de 3 segundos, iniciar ciclo cada 1 segundo
      intervalId = setInterval(cycleLogo, 1000);
    }, 3000);

    // Cleanup: limpiar timeouts e intervalos al desmontar
    return () => {
      clearTimeout(timeoutId);
      if (intervalId) clearInterval(intervalId);
    };
  }, []);

// 🎬 Animación inicial sincronizada
useEffect(() => {
  // 🎬 Timeline sincronizado para que ambos paneles aparezcan al mismo tiempo
  const tl = gsap.timeline();

  // Ambos paneles aparecen simultáneamente
  tl.fromTo(
    [leftPanelRef.current, rightLogoRef.current, mobileLogoRef.current],
    { y: 50, opacity: 0 },
    {
      y: 0,
      opacity: 1,
      duration: 1.2,
      ease: 'power2.out',
      stagger: 0 // Sin delay entre elementos - aparecen juntos
    }
  );

  // Asegura visibilidad del logo al volver de una pestaña oculta
  const handleVisibilityChange = () => {
    if (document.visibilityState === 'visible') {
      gsap.to(logoImgRef.current, { opacity: 1, duration: 0.2 });
    }
  };

  document.addEventListener('visibilitychange', handleVisibilityChange);

  return () => {
    document.removeEventListener('visibilitychange', handleVisibilityChange);
  };
}, []);

// Inicializar monitoreo de seguridad - MODO LOGIN (menos sensible)
useEffect(() => {
  // Iniciar monitoreo en modo login (menos agresivo)
  securityMonitor.startLoginMode();

  // Log de inicio de sesión de login
  logSecurityEvent('LOGIN_PAGE_ACCESSED', {
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    referrer: document.referrer,
    viewport: `${window.innerWidth}x${window.innerHeight}`,
    language: navigator.language,
    platform: navigator.platform
  });

  // Detectar si viene de logout o es primera visita
  const wasLoggedOut = sessionStorage.getItem('wasLoggedOut');
  if (wasLoggedOut) {
    logSecurityEvent('RETURN_AFTER_LOGOUT', {
      timestamp: new Date().toISOString()
    });
    sessionStorage.removeItem('wasLoggedOut');
  }

  // Limpiar al desmontar
  return () => {
    securityMonitor.stop();
  };
}, []);

///-------------------------------------------------------------------------------------------------------------------------------------------------------------------///

  // Hace un POST real al backend cuando esté listo.
  // Imprime el token que reciba (o puedes guardarlo en localStorage si lo necesitas).
  // Muestra errores claros si algo sale mal.

const handleSubmit = async (e) => {
  console.log('🚀 handleSubmit ejecutándose');
  e.preventDefault();
  console.log('🚀 preventDefault ejecutado');

  // 🚫 Prevenir envío múltiple si ya está cargando
  if (authLoading) {
    console.log('🚀 Bloqueado por authLoading');
    return;
  }

  // 🚫 Verificar si está bloqueado por rate limiting
  if (isBlocked) {
    showErrorNotificationWithMessage('⚠️ Demasiados intentos fallidos. Espera 5 minutos antes de intentar nuevamente.');
    logSecurityEvent('BLOCKED_LOGIN_ATTEMPT', {
      email: usuario.substring(0, 3) + '***',
      attempts: loginAttempts
    });
    return;
  }

  // 🧹 Limpiar errores previos
  setErrors({});

  // Sanitizar inputs
  const cleanEmail = sanitizeInput(usuario);
  const cleanPassword = sanitizeInput(password);

  // Validaciones robustas
  const newErrors = {};

  if (!cleanEmail || !cleanPassword) {
    newErrors.general = 'Todos los campos son obligatorios';
  }

  if (cleanEmail && !validateEmail(cleanEmail)) {
    newErrors.email = 'Formato de email inválido';
  }

  if (cleanPassword && !validatePassword(cleanPassword)) {
    newErrors.password = 'La contraseña debe tener entre 6 y 128 caracteres';
  }

  if (Object.keys(newErrors).length > 0) {
    setErrors(newErrors);
    logSecurityEvent('INVALID_INPUT_ATTEMPT', {
      email: cleanEmail.substring(0, 3) + '***',
      errors: Object.keys(newErrors)
    });

    // Mostrar notificación para errores de validación
    const errorMessages = Object.values(newErrors);
    showErrorNotificationWithMessage(`⚠️ ${errorMessages[0]}`);

    return;
  }

  try {
    // 🌐 Llamada directa al backend SQQ
    const response = await authLogin(cleanEmail, cleanPassword);

    // Login exitoso
    setLoginAttempts(0); // Reset intentos

    // Determinar tipo de usuario y ruta
    const userRole = response.user.role;
    const isAdmin = userRole === 'ADMIN' || userRole === 'admin';

    // Normalizar rol para compatibilidad con sistema legacy
    const normalizedRole = isAdmin ? 'admin' : 'usuario';

    // Role normalization completed

    // Actualizar sessionStorage con rol normalizado para compatibilidad
    sessionStorage.setItem('userRole', normalizedRole);

    setUserType(normalizedRole);
    setShowSuccessNotification(true);
    setIsRedirecting(true);

    // Cambiar SecurityMonitor a modo normal después de login exitoso
    securityMonitor.switchToNormalMode();

    setTimeout(() => {
      navigate(isAdmin ? '/admin' : '/usuario');
    }, 2000); // Redirigir después de 2 segundos

  } catch (error) {
    // Error de login
    console.error('Error en login:', error);
    handleLoginFailure(cleanEmail, error.message || 'Error de conexión con el servidor');
  }
};

// Manejo de fallos de login con rate limiting
const handleLoginFailure = (email, errorMessage) => {
  const newAttempts = loginAttempts + 1;
  setLoginAttempts(newAttempts);

  logSecurityEvent('FAILED_LOGIN_ATTEMPT', {
    email: email.substring(0, 3) + '***',
    attempt: newAttempts,
    error: errorMessage,
    ip: 'frontend-log' // En producción, el backend capturaría la IP real
  });

  if (newAttempts >= 5) {
    setIsBlocked(true);
    logSecurityEvent('USER_BLOCKED', {
      email: email.substring(0, 3) + '***',
      totalAttempts: newAttempts
    });

    // Desbloquear después de 5 minutos
    setTimeout(() => {
      setIsBlocked(false);
      setLoginAttempts(0);
      logSecurityEvent('USER_UNBLOCKED', {
        email: email.substring(0, 3) + '***'
      });
    }, 5 * 60 * 1000); // 5 minutos

    showErrorNotificationWithMessage('🚫 Demasiados intentos fallidos. Cuenta bloqueada por 5 minutos.');
  } else {
    showErrorNotificationWithMessage(`⚠️ Cuidado, te quedan ${5 - newAttempts} intentos restantes`);
  }
};




///-------------------------------------------------------------------------------------------------------------------------------------------------------------------///
  // Renderiza el componente de inicio de sesión

  // con un formulario de usuario y contraseña, y un logo que cambia cada segundo.
  // El logo cambia con una animación suave y los paneles tienen una entrada animada.
  // El panel izquierdo contiene el formulario de inicio de sesión y el derecho muestra el logo.
  // El logo cambia cada segundo con una animación de desvanecimiento.
  // El formulario tiene campos para usuario y contraseña, y un botón para enviar.
  // El componente utiliza gsap para las animaciones y tiene un estado para manejar el usuario, contraseña y el índice del logo actual.
  return (
    <div className={`min-h-screen w-full flex flex-col lg:flex-row transition-colors duration-500 ${
      darkMode ? 'bg-gray-800' : 'bg-white'
    }`}>
      {/* Estilos CSS para animaciones del ojo */}
      <style>{eyeAnimationStyles}</style>

      {/* Botón de modo oscuro - Arriba izquierda */}
      <button
        onClick={toggleDarkMode}
        className="absolute top-4 left-4 z-50 p-3 rounded-xl transition-all duration-300 hover:scale-110 active:scale-95 group"
        style={{
          backgroundColor: darkMode ? 'rgba(55, 65, 81, 0.9)' : 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(10px)',
          border: darkMode ? '1px solid rgba(75, 85, 99, 0.3)' : '1px solid rgba(229, 231, 235, 0.3)',
          boxShadow: darkMode
            ? '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)'
            : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
        }}
      >
        {darkMode ? (
          <Sun
            size={20}
            className="text-yellow-400 group-hover:text-yellow-300 transition-all duration-300 group-hover:rotate-12 transform"
          />
        ) : (
          <Moon
            size={20}
            className="text-gray-600 group-hover:text-indigo-600 transition-all duration-300 group-hover:-rotate-12 transform"
          />
        )}
      </button>

      {/* Panel izquierdo - Formulario */}
      <div
        ref={leftPanelRef}
        className="w-full lg:w-1/2 flex flex-col justify-center items-center p-6 sm:p-8 lg:p-10 opacity-0"
      >
        <img
          src={logo1}
          alt="Logo"
          className={`mb-4 sm:mb-6 w-32 sm:w-40 transition-all duration-500 ${
            darkMode ? 'filter brightness-0 invert' : ''
          }`}
        />
        <h2 className={`text-lg sm:text-xl font-semibold mb-2 text-center transition-colors duration-500 ${
          darkMode ? 'text-white' : 'text-gray-800'
        }`}>
          {t('welcome')}
        </h2>
        <p className={`mb-4 sm:mb-6 text-center text-sm sm:text-base transition-colors duration-500 ${
          darkMode ? 'text-gray-300' : 'text-gray-600'
        }`}>
          {t('subtitle')}
        </p>

        {/* 🚨 Mostrar errores de validación */}
        {errors.general && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg text-xs sm:text-sm w-full max-w-sm">
            {errors.general}
          </div>
        )}

        {/* ⚠️ Mostrar estado de bloqueo */}
        {isBlocked && (
          <div className="mb-4 p-3 bg-yellow-100 border border-yellow-400 text-yellow-700 rounded-lg text-xs sm:text-sm w-full max-w-sm">
            🚫 Cuenta temporalmente bloqueada por seguridad. Espera 5 minutos.
          </div>
        )}

        {/* 📊 Mostrar intentos restantes */}
        {loginAttempts > 0 && !isBlocked && (
          <div className="mb-4 p-3 bg-orange-100 border border-orange-400 text-orange-700 rounded-lg text-xs sm:text-sm w-full max-w-sm">
            ⚠️ Intentos fallidos: {loginAttempts}/5. Ten cuidado con las credenciales.
          </div>
        )}

        <form onSubmit={handleSubmit} className="w-full max-w-sm px-4 sm:px-0" autoComplete="off">
          <input
            type="email"
            placeholder={t('email')}
            value={usuario}
            onChange={(e) => setUsuario(e.target.value)}
            className={`w-full px-4 py-3 mb-2 border rounded-xl focus:outline-none focus:ring-2 transition-all duration-300 text-sm sm:text-base ${
              errors.email
                ? 'border-red-400 focus:ring-red-400'
                : darkMode
                  ? 'border-gray-600 focus:ring-indigo-400 bg-gray-800 text-white placeholder-gray-400'
                  : 'border-gray-300 focus:ring-indigo-400 bg-white text-gray-900 placeholder-gray-500'
            }`}
            maxLength="254"
            required
            disabled={isBlocked}
          />
          {errors.email && (
            <p className="text-red-600 text-xs mb-2">{errors.email}</p>
          )}

          <div className="relative mb-2">
            <input
              type={showPassword ? "text" : "password"}
              placeholder={t('password')}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className={`w-full px-4 py-3 pr-14 border rounded-xl focus:outline-none focus:ring-2 transition-all duration-300 text-sm sm:text-base ${
                errors.password
                  ? 'border-red-400 focus:ring-red-400'
                  : darkMode
                    ? 'border-gray-600 focus:ring-indigo-400 bg-gray-800 text-white placeholder-gray-400'
                    : 'border-gray-300 focus:ring-indigo-400 bg-white text-gray-900 placeholder-gray-500'
              }`}
              maxLength="128"
              minLength="6"
              required
              disabled={isBlocked}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 transition-all duration-300 hover:scale-110 active:scale-95 group"
              disabled={isBlocked}
              tabIndex={-1}
              style={{ marginTop: '-1px' }}
            >
              <div className="relative">
                <div className={`transition-all duration-500 ease-in-out ${showPassword ? 'animate-pulse' : ''}`}>
                  {showPassword ? (
                    <div className="relative">
                      {/* Ojo abierto con animación */}
                      <svg
                        width="22"
                        height="22"
                        viewBox="0 0 24 24"
                        fill="none"
                        className="text-gray-500 group-hover:text-indigo-600 transition-all duration-300 transform group-hover:scale-105"
                      >
                        <path
                          d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5z"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="animate-[fadeIn_0.3s_ease-in-out]"
                        />
                        <circle
                          cx="12"
                          cy="12"
                          r="3"
                          stroke="currentColor"
                          strokeWidth="2"
                          className="animate-[scaleIn_0.4s_ease-in-out]"
                        />
                      </svg>
                    </div>
                  ) : (
                    <div className="relative">
                      {/* Ojo cerrado con animación */}
                      <svg
                        width="22"
                        height="22"
                        viewBox="0 0 24 24"
                        fill="none"
                        className="text-gray-500 group-hover:text-indigo-600 transition-all duration-300 transform group-hover:scale-105"
                      >
                        <path
                          d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="animate-[fadeIn_0.3s_ease-in-out]"
                        />
                        <path
                          d="M1 1l22 22"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="animate-[drawLine_0.5s_ease-in-out]"
                        />
                      </svg>
                    </div>
                  )}
                </div>
              </div>
            </button>
          </div>
          {errors.password && (
            <p className="text-red-600 text-xs mb-2">{errors.password}</p>
          )}
          <button
            type="submit"
            disabled={isBlocked || authLoading}
            className={`w-full flex items-center justify-center gap-3 py-3 px-6 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group ${
              isBlocked || authLoading
                ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white'
            }`}
          >
            {!isBlocked && !authLoading && (
              <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            )}
            {authLoading && (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            )}
            <span className="font-light tracking-wide">
              {isBlocked ? t('blocked') : authLoading ? t('loggingIn') : t('login')}
            </span>
          </button>
        </form>
      </div>

      {/* Panel derecho - Logos cambiando (Desktop) */}
      <div
        ref={rightLogoRef}
        className="hidden lg:flex w-full lg:w-1/2 flex-col justify-center items-center p-6 sm:p-8 lg:p-10 opacity-0"
      >
        <img
          ref={logoImgRef}
          src={logoList[currentLogoIndex]}
          alt="Logo Quantum"
          className={`w-2/3 max-w-md opacity-100 transition-all duration-500 ${
            darkMode ? 'filter brightness-0 invert' : ''
          }`}
        />
      </div>

      {/* Panel móvil - Logos cambiando (Mobile/Tablet) */}
      <div
        ref={mobileLogoRef}
        className="flex lg:hidden w-full flex-col justify-center items-center p-6 sm:p-8 opacity-0"
      >
        <img
          src={logoList[currentLogoIndex]}
          alt="Logo Quantum"
          className={`w-3/4 sm:w-2/3 max-w-xs opacity-100 transition-all duration-500 ${
            darkMode ? 'filter brightness-0 invert' : ''
          }`}
        />
      </div>

      {/* Notificaciones ahora manejadas por React Hot Toast */}

      {/* 🎉 Notificación de éxito elegante como antes */}
      {showSuccessNotification && (
        <div className="fixed bottom-4 sm:bottom-8 left-1/2 transform -translate-x-1/2 z-50 px-4">
          <div className="bg-green-500 text-white px-4 sm:px-8 py-3 sm:py-4 rounded-lg shadow-2xl flex items-center gap-3 animate-bounce max-w-sm sm:max-w-none">
            <div className="w-5 h-5 sm:w-6 sm:h-6 bg-white rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-green-500 font-bold text-sm sm:text-lg">✓</span>
            </div>
            <div className="min-w-0">
              <p className="font-bold text-sm sm:text-lg">{t('loginSuccess')}</p>
              <p className="text-xs sm:text-sm opacity-90 truncate">
                {isRedirecting
                  ? (userType === 'admin'
                      ? t('redirectingAdmin')
                      : t('redirectingUser'))
                  : (userType === 'admin'
                      ? 'Bienvenido Admin Quantum'
                      : 'Bienvenido Usuario Quantum')
                }
              </p>
            </div>
            {isRedirecting && (
              <div className="ml-2 sm:ml-4 flex-shrink-0">
                <div className="w-4 h-4 sm:w-5 sm:h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 🚨 Notificación de error elegante como antes */}
      {showErrorNotification && (
        <div className="fixed bottom-4 sm:bottom-8 left-1/2 transform -translate-x-1/2 z-50 px-4">
          <div className="bg-red-500 text-white px-4 sm:px-8 py-3 sm:py-4 rounded-lg shadow-2xl flex items-center gap-3 animate-bounce max-w-sm sm:max-w-md">
            <div className="w-5 h-5 sm:w-6 sm:h-6 bg-white rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-red-500 font-bold text-sm sm:text-lg">✗</span>
            </div>
            <div className="min-w-0">
              <p className="font-bold text-sm sm:text-lg">{t('loginError')}</p>
              <p className="text-xs sm:text-sm opacity-90 break-words">
                {errorMessage}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Login;
