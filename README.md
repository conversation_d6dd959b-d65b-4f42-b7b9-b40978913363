# SQQ Frontend

Sistema de gestión de llaves cuánticas desarrollado con React y Vite. Proporciona una interfaz de usuario para la administración de usuarios y gestión de llaves cuánticas con autenticación basada en roles.

## Descripción

SQQ Frontend es la interfaz de usuario del sistema SQQ (Secure Quantum System), que permite a los usuarios gestionar llaves cuánticas de forma segura. El sistema incluye dashboards diferenciados para administradores y usuarios regulares, con funcionalidades de autenticación, gestión de perfiles e internacionalización.

## Tabla de Contenidos

- [Características](#características)
- [Tecnologías](#tecnologías)
- [Instalación](#instalación)
- [Configuración](#configuración)
- [Uso](#uso)
- [Estructura del Proyecto](#estructura-del-proyecto)
- [API Integration](#api-integration)
- [Autenticación](#autenticación)
- [Despliegue](#despliegue)
- [Contribución](#contribución)

## Características

- **Autenticación y autorización**: Sistema de login con roles diferenciados (admin/usuario)
- **Gestión de usuarios**: CRUD completo para administradores
- **Gestión de llaves cuánticas**: Visualización, creación y administración de llaves
- **Dashboard responsivo**: Interfaz adaptable a diferentes dispositivos
- **Internacionalización**: Soporte para español e inglés
- **Modo oscuro/claro**: Toggle de tema con persistencia
- **Seguridad**: Implementación de medidas de protección frontend
- **Animaciones**: Transiciones suaves con GSAP

## Tecnologías

- **React** 18.2.0 - Framework de interfaz de usuario
- **Vite** 4.4.0 - Build tool y servidor de desarrollo
- **Tailwind CSS** 3.3.0 - Framework de estilos utilitarios
- **React Router DOM** 6.8.0 - Enrutamiento para SPA
- **GSAP** 3.12.0 - Librería de animaciones
- **Lucide React** - Iconografía moderna
- **i18next** - Internacionalización
- **React i18next** - Integración de i18next con React

## Instalación

### Prerrequisitos

- Node.js 18 o superior
- npm o yarn

### Pasos de instalación

1. Clonar el repositorio:
```bash
git clone https://github.com/tu-usuario/sqq-frontend.git
cd sqq-frontend
```

2. Instalar dependencias:
```bash
npm install
```

3. Configurar variables de entorno:
```bash
cp .env.example .env
```

4. Ejecutar en modo desarrollo:
```bash
npm run dev
```

5. Abrir en el navegador:
```
http://localhost:5173
```

## Configuración

### Variables de entorno

Crear un archivo `.env` en la raíz del proyecto:

```env
VITE_API_BASE_URL=http://localhost:3000
VITE_API_TIMEOUT=30000
VITE_NODE_ENV=development
```

### Scripts disponibles

```bash
npm run dev          # Servidor de desarrollo
npm run build        # Build para producción
npm run preview      # Vista previa del build
npm run lint         # Ejecutar ESLint
```

## Uso

### Credenciales de acceso

#### Administrador
- Email: `<EMAIL>`
- Contraseña: `cityslicka`

#### Usuario regular
- Email: `<EMAIL>`
- Contraseña: `cityslicka`

### Funcionalidades principales

#### Dashboard de administrador
- Gestión de usuarios (crear, editar, eliminar)
- Visualización de estadísticas del sistema
- Gestión de llaves API por usuario
- Configuración del sistema

#### Dashboard de usuario
- Gestión personal de llaves cuánticas
- Edición de perfil personal
- Cambio de contraseña
- Visualización de información de cuenta

### Navegación

El sistema utiliza React Router para la navegación:
- `/` - Página de login
- `/admin` - Dashboard de administrador (requiere rol admin)
- `/usuario` - Dashboard de usuario (requiere rol usuario)

## Estructura del Proyecto

```
src/
├── components/           # Componentes reutilizables
│   ├── Login.jsx        # Componente de autenticación
│   ├── RutaProtegida.jsx # Protección de rutas
│   ├── common/          # Componentes comunes
│   │   ├── LanguageSelector.jsx
│   │   ├── Modal.jsx
│   │   ├── Sidebar.jsx
│   │   └── MainContent.jsx
│   └── dashboard/       # Componentes de dashboard
│       └── user/        # Dashboard de usuario
│           ├── KeyManagement.jsx
│           ├── ProfileManagement.jsx
│           └── modals/  # Modales especializados
├── contexts/            # Contextos de React
│   └── LanguageContext.jsx
├── hooks/               # Hooks personalizados
│   ├── useAuth.js
│   ├── useLanguage.js
│   ├── useDarkMode.js
│   └── useSecurity.js
├── pages/               # Páginas principales
│   ├── AdminDashboard.jsx
│   └── UsuarioDashboard.jsx
├── services/            # Servicios de API
│   ├── config/
│   │   └── apiConfig.js
│   ├── auth/
│   │   └── authService.js
│   ├── users/
│   │   └── userService.js
│   └── keys/
│       └── keyService.js
├── utils/               # Utilidades
│   ├── SecureStorage.js
│   ├── SecurityLogger.js
│   ├── SecurityMonitor.js
│   └── i18n.js
├── locales/             # Archivos de traducción
│   ├── en/
│   │   └── translation.json
│   └── es/
│       └── translation.json
├── App.jsx              # Componente principal
└── main.jsx             # Punto de entrada
```

## API Integration

### Configuración de servicios

El frontend se conecta al backend a través de servicios organizados por módulos:

#### Configuración base
```javascript
// src/services/config/apiConfig.js
export const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
};
```

#### Servicios disponibles
- **authService**: Manejo de autenticación y tokens
- **userService**: Operaciones CRUD de usuarios
- **keyService**: Gestión de llaves cuánticas

### Endpoints principales

```javascript
// Autenticación
POST /auth/login
POST /auth/refresh
GET  /auth/profile

// Usuarios
GET    /users
POST   /users
PUT    /users/:id
DELETE /users/:id

// Llaves
GET  /keys
POST /keys
PUT  /keys/:id
POST /keys/upload-to-ctm
```

## Autenticación

### Sistema de roles

El sistema implementa autenticación basada en roles:

- **admin**: Acceso completo al sistema, gestión de usuarios
- **usuario**: Acceso limitado, gestión personal de llaves

### Flujo de autenticación

1. Usuario ingresa credenciales en el formulario de login
2. Frontend envía petición al endpoint `/auth/login`
3. Backend valida credenciales y genera tokens JWT
4. Tokens se almacenan de forma segura en el frontend
5. Requests posteriores incluyen el token en headers
6. Middleware de autenticación valida tokens en cada request

### Protección de rutas

```javascript
// Componente RutaProtegida
<Route path="/admin" element={
  <RutaProtegida requiredRole="admin">
    <AdminDashboard />
  </RutaProtegida>
} />
```

### Gestión de sesiones

- Tokens almacenados en sessionStorage con encriptación
- Expiración automática de sesiones
- Renovación automática de tokens
- Logout seguro con limpieza de datos

## Despliegue

### Build para producción

```bash
npm run build
```

Los archivos optimizados se generan en la carpeta `dist/`.

### Variables de entorno para producción

```env
VITE_API_BASE_URL=https://api.tudominio.com
VITE_NODE_ENV=production
```

### Despliegue con Docker

```dockerfile
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## Contribución

### Cómo contribuir

1. Fork el repositorio
2. Crear rama para nueva funcionalidad (`git checkout -b feature/nueva-funcionalidad`)
3. Commit cambios (`git commit -m 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

### Estándares de código

- Usar ESLint para mantener consistencia
- Componentes funcionales con hooks
- Tailwind CSS para estilos
- Documentar funciones complejas

## Licencia

Este proyecto está bajo la Licencia MIT.

## Equipo

- **Frontend Developer**: Cedric Lavin
- **Backend Developer**: Nicolás Rivas
- **Email**: <EMAIL>
